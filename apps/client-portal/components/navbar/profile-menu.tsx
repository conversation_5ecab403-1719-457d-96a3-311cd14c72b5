"use client";

import { useClerk, useUser } from "@clerk/nextjs";
import {
  Book,
  CircleHelp,
  CircleUserRound,
  LogOut,
  TriangleAlert,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { Link as RouterLink } from "@/components/shared/link";
import { useAuthentication } from "@/stores/authentication";
import { clearAllStores } from "@/utils/clear-stores";

import { Dropdown, DropdownContent, DropdownTrigger } from "../ui/dropdown";

export const ProfileMenu = () => {
  const { signOut: clerkSignOut, openUserProfile } = useClerk();
  const { user: clerkUser } = useUser();

  const { user } = useAuthentication();
  const firstLetter = user?.firstName?.[0]?.toUpperCase() || "U";

  const handleSignOut = async () => {
    // Sign out from Clerk
    await clerkSignOut();

    // Clear all global stores
    clearAllStores();
  };

  return (
    <Dropdown className="flex items-center" placement="bottom-end">
      <DropdownTrigger>
        <button className="flex items-center" id="profile-btn">
          {clerkUser?.imageUrl ? (
            <Image
              src={clerkUser.imageUrl}
              alt={`${user?.firstName || ""} ${user?.lastName || ""}`}
              width={30}
              height={30}
              className="size-[30px] cursor-pointer select-none rounded-full object-cover"
            />
          ) : (
            <span className="font-plus-jakarta size-[30px] select-none rounded-full bg-purple-500 text-[18px] font-medium leading-[22px] text-white">
              {firstLetter}
            </span>
          )}
        </button>
      </DropdownTrigger>

      <DropdownContent className="motion-preset-slide-down-sm mt-2 rounded-t-none">
        <div className="flex items-center gap-2.5 border-b px-5 py-2.5">
          {clerkUser?.imageUrl ? (
            <Image
              src={clerkUser.imageUrl}
              alt={`${user?.firstName || ""} ${user?.lastName || ""}`}
              width={50}
              height={50}
              className="size-[50px] select-none rounded-full object-cover"
            />
          ) : (
            <div className="font-plus-jakarta flex size-[50px] select-none items-center justify-center rounded-full bg-orange-500 text-center text-[24px] font-medium leading-[22px] text-white">
              <span className="select-none">{firstLetter}</span>
            </div>
          )}

          <div className="flex flex-col">
            <span className="font-poppins text-xl font-bold leading-[35px]">
              {user?.firstName} {user?.lastName}
            </span>
            <span className="font-poppins text-base capitalize leading-[22px]">
              {user?.currentProfile?.type} User
            </span>
          </div>
        </div>

        <div className="font-poppins text-sm font-semibold leading-[22px]">
          <div
            className="flex cursor-pointer items-center gap-2.5 px-5 py-3 hover:text-purple-500"
            onClick={() => openUserProfile()}
          >
            <CircleUserRound className="size-5" />
            <span>My Profile</span>
          </div>
          <RouterLink
            href="/training"
            className="flex cursor-pointer items-center gap-2.5 px-5 py-3 hover:text-purple-500 focus-visible:outline-none"
          >
            <Book className="size-5" />
            <span>Clincove Training</span>
          </RouterLink>
          <div className="flex cursor-pointer items-center gap-2.5 px-5 py-3 hover:text-purple-500">
            <CircleHelp className="size-5" />
            <span>Help</span>
          </div>
          <Link
            href={`https://airtable.com/app8mAKkCGVHPyX49/shrM1oGnYTPUQGuLx?prefill_User=${user?.email}`}
            target="_blank"
            className="flex cursor-pointer items-center gap-2.5 px-5 py-3 hover:text-purple-500 focus-visible:outline-none"
          >
            <TriangleAlert className="size-5" />
            <span>Report Issue</span>
          </Link>
          <button
            onClick={async () => {
              await handleSignOut();
            }}
            className="flex cursor-pointer items-center gap-2.5 px-5 py-3 hover:text-purple-500"
          >
            <LogOut className="size-5" />
            <span>Sign Out</span>
          </button>
        </div>
      </DropdownContent>
    </Dropdown>
  );
};
