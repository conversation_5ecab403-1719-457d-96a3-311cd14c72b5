"use client";

import { useAuth } from "@clerk/nextjs";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import type { PropsWithChildren } from "react";
import { useEffect } from "react";
import { AiOutlineLoading } from "react-icons/ai";
import { CiWarning } from "react-icons/ci";

import { Button } from "@/components/ui/button";
import { useAuthActions, useAuthenticated, useAuthState } from "@/hooks/auth";
import { useAutoLoadPersistedStudies } from "@/hooks/studies/use-auto-load-persisted-studies";
import { useAuthenticationStore } from "@/stores/authentication";
import { useUserTypeStore } from "@/stores/user-type";

export const Authenticate = ({ children }: PropsWithChildren) => {
  const queryClient = useQueryClient();
  const { refetch, isPending } = useAuthenticated();
  const { isLoaded, isSignedIn, signOut } = useAuth();
  const { signOut: signOutAuth } = useAuthActions();
  const { userType } = useAuthState();
  const { setUnauthenticated } = useAuthenticationStore();
  const { setUserType } = useUserTypeStore();
  const router = useRouter();

  useAutoLoadPersistedStudies();

  useEffect(() => {
    if (!isLoaded) return;
    if (isSignedIn) {
      refetch();
    } else {
      setUnauthenticated();
      setUserType(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded, isSignedIn]);

  const handleCancel = async () => {
    await signOut();
    setUnauthenticated();
    signOutAuth();
    queryClient.removeQueries();
    router.push("/authentication/sign-in");
  };

  const handleGoToAdminPortal = async () => {
    const domain = window.location.origin;
    const adminDomain = domain.replace("-app", "-admin");
    await signOut({
      redirectUrl: `${adminDomain}/authentication/sign-in`,
    });
    signOutAuth();
    queryClient.removeQueries();
  };

  if (isLoaded && !isPending && userType && userType === "clincove") {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50">
        <div className="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="mb-6 flex items-center justify-center">
            <div className="rounded-full bg-red-100 p-3">
              <CiWarning className="size-8 text-red-600" />
            </div>
          </div>

          <h2 className="mb-4 text-center text-xl font-semibold text-gray-900">
            Access Denied
          </h2>

          <p className="mb-8 text-center text-gray-600">
            You are trying to login to the Client portal with the an Admin
            account.
          </p>

          <div className="space-y-3">
            <Button
              className="w-full"
              variant="primary"
              onClick={handleGoToAdminPortal}
            >
              Go to Admin Portal
            </Button>

            <Button className="w-full" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!isLoaded || isPending || !userType) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <AiOutlineLoading className="h-10 w-10 animate-spin" />
      </div>
    );
  }

  return children;
};
