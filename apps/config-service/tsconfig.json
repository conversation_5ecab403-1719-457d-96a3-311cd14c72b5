{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "strict": true, "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "../../apps/config-service/.next/types/**/*.ts", "../../dist/apps/config-service/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}