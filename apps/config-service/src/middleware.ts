import { clerkMiddleware } from "@clerk/nextjs/server";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

const publicPaths = ["/authentication/sign-in"];

const isPublic = (path: string) => {
  return publicPaths.some((x) => path === x);
};

export default clerkMiddleware(async (auth, req: NextRequest) => {
  const { userId, sessionClaims } = await auth();

  const path = req.nextUrl.pathname;

  // If authenticated users try to access sign-in page, redirect to root
  if (userId && path === "/authentication/sign-in") {
    return NextResponse.redirect(new URL("/", req.url));
  }

  // Allow public routes
  if (isPublic(path)) {
    return NextResponse.next();
  }

  // If user is not authenticated and tries to access sign-in page
  if (!userId && path === "/authentication/sign-in") {
    return NextResponse.next();
  }

  // For authenticated users
  if (userId) {
    try {
      // Get user's metadata from session claims
      const metadata =
        (sessionClaims?.publicMetadata as {
          region?: string;
          groupId?: string;
        }) || {};

      const currentDomain = req.headers.get("host") || "";

      // Check if user has metadata with region
      if (
        metadata.region &&
        metadata.groupId &&
        !currentDomain.includes("localhost")
      ) {
        const expectedDomain = `.${metadata.region}.`;

        // If domain doesn't match the region, redirect to correct domain with the same path
        if (!currentDomain.includes(expectedDomain)) {
          const protocol =
            process.env.NODE_ENV === "production" ? "https" : "http";
          const newDomain = currentDomain.replace(/\.[^.]+\./, expectedDomain);
          return NextResponse.redirect(`${protocol}://${newDomain}${path}`);
        }
      }

      // For paths starting with /authentication, redirect to root
      if (path.startsWith("/authentication")) {
        return NextResponse.redirect(new URL("/", req.url));
      }

      // Allow access to other routes for authenticated users
      return NextResponse.next();
    } catch (error) {
      console.error("Error in middleware:", error);
      return NextResponse.next();
    }
  }

  // Default: redirect unauthenticated users to sign-in
  return NextResponse.redirect(new URL("/authentication/sign-in", req.url));
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
