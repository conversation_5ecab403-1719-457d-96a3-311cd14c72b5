import { admin } from "@clincove-eng/backend-sdk-temp";

import Base<PERSON><PERSON> from "../base";
import type {
  AuthenticatedUser,
  LoginPayload,
  LoginResponse,
  ResetPasswordPayload,
  UpdatePasswordPayload,
} from "./types";

export class Auth<PERSON><PERSON> extends BaseApi {
  public constructor() {
    super("/auth", true);
  }

  public async login(params: LoginPayload): Promise<LoginResponse> {
    return this.http.post<LoginResponse>("/login", params);
  }

  public async logout(): Promise<void> {
    return this.http.post<void>("/logout");
  }

  public async authenticated(): Promise<AuthenticatedUser> {
    return admin.Auth.isAuthenticated() as unknown as Promise<AuthenticatedUser>;
  }

  public async requestResetPassword(params: { email: string }): Promise<void> {
    return this.http.post<void>("/request-password-reset", params);
  }

  public async resetPassword(params: ResetPasswordPayload): Promise<void> {
    return this.http.post<void>("/reset-password", params);
  }

  public async updatePassword(params: UpdatePasswordPayload): Promise<void> {
    return this.http.post<void>("/update-password", params);
  }
}
