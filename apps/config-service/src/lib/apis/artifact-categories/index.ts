import BaseApi from "../base";
import { MetadataParams } from "../types";
import type {
  AddCategoryVersionPayload,
  Artifact,
  ArtifactCategoriesFilter,
  ArtifactCategoriesResponse,
  ArtifactCategory,
  ArtifactVersion,
  AutoCategorizePayload,
  AutoCategorizeResponse,
  CategoryVersionListResponse,
  CreateArtifactCategoryPayload,
  IsfSection,
  IsfZone,
  UpdateArtifactCategoryPayload,
  UpdateCategoryVersionPayload,
} from "./types";

class ArtifactCategoriesApi extends BaseApi {
  constructor() {
    super("/artifact-categories", true);
  }

  public async list(
    params?: ArtifactCategoriesFilter,
  ): Promise<ArtifactCategoriesResponse> {
    const queryParams = params ? this.generateQueryParams(params) : "";
    return this.http.get<ArtifactCategoriesResponse>(`?${queryParams}`);
  }

  public async listLatestVersions(
    params?: ArtifactCategoriesFilter,
  ): Promise<ArtifactCategoriesResponse> {
    return this.http.get<ArtifactCategoriesResponse>(`/`, params);
  }

  public async getById(id: string): Promise<ArtifactCategory> {
    return this.http.get<ArtifactCategory>(`/${id}`);
  }

  public async create(
    payload: CreateArtifactCategoryPayload,
  ): Promise<ArtifactCategory> {
    return this.http.post<ArtifactCategory>(``, payload);
  }

  public async update(
    id: string,
    payload: UpdateArtifactCategoryPayload,
  ): Promise<ArtifactCategory> {
    return this.http.put<ArtifactCategory>(`/${id}`, payload);
  }

  public async archive(id: string): Promise<ArtifactCategory> {
    return this.http.patch<ArtifactCategory>(`/${id}/archive`);
  }

  public async export(version?: string) {
    return this.http.get<Blob>(
      `/export${version ? `?version=${version}` : ""}`,
    );
  }

  async import(payload: { formData: FormData }) {
    return this.http.post(`/import`, payload.formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  public async getVersions(params?: MetadataParams) {
    const queryParams = params ? this.generateQueryParams(params) : "";
    return this.http.get<CategoryVersionListResponse>(
      `/versions?${queryParams}`,
    );
  }

  public async createVersion(payload: AddCategoryVersionPayload) {
    return this.http.post<ArtifactVersion>(`/versions`, payload);
  }

  public async updateVersion(payload: UpdateCategoryVersionPayload) {
    return this.http.patch<ArtifactVersion>(`/versions/${payload.id}`, payload);
  }

  public async deleteVersion(id: string) {
    return this.http.delete(`/versions/${id}`);
  }

  public async getArtifactZones() {
    return this.http.get<IsfZone[]>(`/isf/zones`);
  }

  public async getArtifactSections(zone: string) {
    const params = this.generateQueryParams({ zone });
    return this.http.get<IsfSection[]>(`/isf/sections?${params}`);
  }

  public async getArtifacts(zone: string, section: string) {
    const params = this.generateQueryParams({ zone, section });
    return this.http.get<Artifact[]>(`/isf/artifacts?${params}`);
  }

  public async autoCategorize(payload: AutoCategorizePayload) {
    return this.http.post<AutoCategorizeResponse[]>(
      `/categorize-filenames`,
      payload,
    );
  }
}

export const artifactCategories = new ArtifactCategoriesApi();
export * from "./types";
