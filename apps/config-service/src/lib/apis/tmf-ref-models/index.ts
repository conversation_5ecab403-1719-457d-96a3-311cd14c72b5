import Base<PERSON>pi from "../base";
import { MetadataParams } from "../types";
import type {
  AddTMFRefModelPayload,
  TMFRefModel,
  TMFRefModelListResponse,
  UpdateTMFRefModelPayload,
} from "../artifact-categories/types";

class TMFRefModelApi extends BaseApi {
  constructor() {
    super("/artifact-tmf-ref-model", true);
  }

  public async create(payload: AddTMFRefModelPayload) {
    return this.http.post<TMFRefModel>("/", payload);
  }

  public async list(params?: MetadataParams) {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<TMFRefModelListResponse>(`?${paramUrl}`);
  }

  public async update(payload: UpdateTMFRefModelPayload) {
    return this.http.put<TMFRefModel>(`/${payload.id}`, payload);
  }

  public async delete(id: string) {
    return this.http.delete(`/${id}`);
  }
}

export const tmfRefModelApi = new TMFRefModelApi();
export * from "../artifact-categories/types";
