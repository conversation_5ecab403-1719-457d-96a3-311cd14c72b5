import "./global.css";

import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { Flowbite } from "flowbite-react";
import type { Metadata } from "next";
import { Inter, Plus_Jakarta_Sans, Poppins } from "next/font/google";
import type { PropsWithChildren } from "react";
import { Toaster } from "react-hot-toast";

import { Providers } from "@/contexts/providers";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});
const poppins = Poppins({
  subsets: ["latin"],
  display: "swap",
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});
const plusJakartaSans = Plus_Jakarta_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-plus-jakarta-sans",
});

function cn(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export const metadata: Metadata = {
  title: "Clincove Config Service",
  description: "Configuration and settings management service",
};

export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <ClerkProvider
      appearance={{
        variables: { colorPrimary: "#1D4ED8" },
      }}
    >
      <html lang="en">
        <body
          className={cn(
            "bg-gray-100",
            inter.className,
            poppins.variable,
            plusJakartaSans.variable,
            inter.variable,
          )}
        >
          <Providers>
            <Flowbite theme={{ mode: "light" }}>{children}</Flowbite>
          </Providers>
          <Toaster
            toastOptions={{
              success: {
                className: cn(
                  "!bg-green-100 !border-l-8 !border-green-500 !py-2.5 !pr-6 !font-bold !text-base !shadow-lg",
                  "[&>div:first-child]:w-0 !items-start !justify-start !block !pl-2 min-w-fit",
                ),
                icon: "",
              },
              error: {
                className: cn(
                  "!bg-red-100 !border-l-8 !border-red-500 !py-2.5 !pr-6 !font-bold !text-base !shadow-lg",
                  "[&>div:first-child]:w-0 !items-start !justify-start !block !pl-2 min-w-fit",
                ),
                icon: "",
              },
            }}
          />
        </body>
      </html>
    </ClerkProvider>
  );
}
