"use client";

import { Download, FileUp, Import } from "lucide-react";
import { useRef, useState } from "react";
import { toast } from "react-hot-toast";

import { Button } from "@/components";
import { artifactCategories } from "@/lib/apis/artifact-categories";

interface ImportExportActionsProps {
  selectedVersionId: string;
}

export const ImportExportActions = ({
  selectedVersionId,
}: ImportExportActionsProps) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = async () => {
    if (!selectedVersionId) {
      toast.error("Please select a version to export");
      return;
    }

    setIsExporting(true);
    try {
      const blob = await artifactCategories.export(selectedVersionId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `artifact-categories-v${selectedVersionId}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Categories exported successfully");
    } catch (error) {
      console.error(error);
      toast.error("Failed to export categories");
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!selectedVersionId) {
      toast.error("Please select a version to import to");
      return;
    }

    setIsImporting(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("versionId", selectedVersionId);

      await artifactCategories.import({ formData });
      toast.success("Categories imported successfully");

      // Trigger a refetch of the categories
      window.location.reload(); // Simple approach - you could use queryClient.invalidateQueries instead

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to import categories");
    } finally {
      setIsImporting(false);
    }
  };

  const handleDownloadTemplate = () => {
    // Create a simple CSV template
    const headers = [
      "recordType",
      "tmfZoneName",
      "tmfSectionName",
      "tmfRecordGroupName",
      "isfZoneName",
      "isfSectionName",
      "isfRecordGroupName",
      "alternativeNames",
      "description",
      "isTMF",
      "isISF",
      "requiresSignature",
      "expires",
      "inspectableRecord",
      "includesPHI",
      "origin",
      "tmfCore",
      "iitStudyArtifacts",
      "isActive",
    ];

    const csvContent = headers.join(",") + "\n";
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "artifact-categories-template.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={handleExport}
        disabled={!selectedVersionId || isExporting}
        isLoading={isExporting}
        className="flex items-center gap-2"
      >
        <FileUp size={18} className="text-gray-500" />
        Export
      </Button>

      <Button
        variant="outline"
        onClick={handleImportClick}
        disabled={!selectedVersionId || isImporting}
        isLoading={isImporting}
        className="flex items-center gap-2"
      >
        <Import size={18} className="text-gray-500" />
        Import
      </Button>

      <Button
        variant="outline"
        onClick={handleDownloadTemplate}
        className="flex items-center gap-2"
      >
        <Download size={18} className="text-gray-500" />
        Template
      </Button>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileChange}
        className="hidden"
      />
    </>
  );
};
