"use client";

import { useQuery } from "@tanstack/react-query";

import { Select } from "@/components/ui/form/select";
import { artifactCategories } from "@/lib/apis/artifact-categories";

export const CategoryVersionSelect = () => {
  const { data, isLoading } = useQuery({
    queryKey: ["category-versions-all"],
    queryFn: () =>
      artifactCategories.getVersions({
        limit: 1000, // Load all versions
        orderBy: "version",
        orderDirection: "desc",
      }),
  });

  const options =
    data?.results?.map((version) => ({
      value: version.version.toString(),
      label: `Version ${version.version} (${new Date(version.effectiveDate).toLocaleDateString()})`,
    })) || [];

  return (
    <div className="max-w-md">
      <Select
        name="selectedVersionId"
        placeholder={
          isLoading ? "Loading versions..." : "Choose a category version..."
        }
        options={options}
        isDisabled={isLoading}
      />
    </div>
  );
};
