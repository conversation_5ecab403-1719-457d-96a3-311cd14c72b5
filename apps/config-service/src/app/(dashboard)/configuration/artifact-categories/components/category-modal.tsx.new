"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { theme } from "flowbite-react";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>eader,
} from "@/components";
import { Checkbox, Input, Label, Select, Textarea } from "@/components/ui/form";
import { LazySelect } from "@/components/ui/lazy-select";
import { ArtifactCategory } from "@/lib/apis/artifact-categories";
import { cn } from "@/lib/utils";
import { capitalize } from "@/lib/utils/string";

import {
  useCreateArtifactCategory,
  useUpdateArtifactCategory,
} from "../hooks/use-artifact-category-mutations";
import { useInfiniteCategoryVersion } from "../hooks/use-infinite-category-versions";
import { useInfiniteISFRefModels } from "../hooks/use-infinite-isf-ref-models";
import { useInfiniteTMFRefModels } from "../hooks/use-infinite-tmf-ref-models";

export const IIT_STUDY_ARTIFACTS = [
  "recommended",
  "dependent",
  "mandatory",
] as const;

export const TMF_CORE = ["recommended", "core"] as const;

export const ORIGINS = [
  { label: "TMF", value: "to_TMF" },
  { label: "ISF", value: "to_ISF" },
] as const;

const TMF_FIELDS = [
  "tmfZoneNumber",
  "tmfZoneName",
  "tmfSectionNumber",
  "tmfSectionName",
  "tmfRecordGroupNumber",
  "tmfRecordGroupName",
] as const;

const ISF_FIELDS = [
  "isfZoneNumber",
  "isfZoneName",
  "isfSectionNumber",
  "isfSectionName",
  "isfRecordGroupNumber",
  "isfRecordGroupName",
] as const;

export const baseSchema = z.object({
  tmfRefModelId: z
    .string({ required_error: "TMF ref model is required" })
    .min(1, "TMF ref model is required"),
  isfRefModelId: z
    .string({ required_error: "ISF ref model is required" })
    .min(1, "ISF ref model is required"),
  categoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),

  tmfZoneNumber: z.string().nullable().optional(),
  tmfZoneName: z.string().nullable().optional(),
  tmfSectionNumber: z.string().nullable().optional(),
  tmfSectionName: z.string().nullable().optional(),
  tmfRecordGroupNumber: z.string().nullable().optional(),
  tmfRecordGroupName: z.string().nullable().optional(),

  isfZoneNumber: z.string().nullable().optional(),
  isfZoneName: z.string().nullable().optional(),
  isfSectionNumber: z.string().nullable().optional(),
  isfSectionName: z.string().nullable().optional(),
  isfRecordGroupNumber: z.string().nullable().optional(),
  isfRecordGroupName: z.string().nullable().optional(),

  recordType: z
    .string({
      invalid_type_error: "Record type is required",
      required_error: "Record type is required",
    })
    .min(1, "Record type is required"),
  alternativeNames: z.string().nullable().optional(),

  description: z.string().nullable().optional(),
  requiresSignature: z.boolean().nullable().optional(),
  expires: z.boolean().nullable().optional(),
  inspectableRecord: z.boolean().nullable().optional(),
  includesPHI: z.boolean().nullable().optional(),
  origin: z.enum(["to_ISF", "to_TMF", ""]).nullable().optional(),
  isTMF: z.boolean().default(false),
  isISF: z.boolean().default(false),
  tmfCore: z
    .enum([...TMF_CORE, ""])
    .nullable()
    .optional(),
  iitStudyArtifacts: z.enum(IIT_STUDY_ARTIFACTS, {
    errorMap: () => ({
      message: "Investigator Initiated Study Artifacts is required",
    }),
  }),
  isActive: z.boolean().default(true),
});

const categorySchema = z.preprocess((input, ctx) => {
  const isfFields = baseSchema
    .pick({
      isISF: true,
      isfRecordGroupName: true,
      isfRecordGroupNumber: true,
      isfSectionName: true,
      isfSectionNumber: true,
      isfZoneName: true,
      isfZoneNumber: true,
    })
    .safeParse(input);
  if (isfFields.success) {
    const data = isfFields.data;
    if (data.isISF) {
      if (!data.isfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Name is required",
          path: ["isfRecordGroupName"],
        });
      }
      if (!data.isfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Number is required",
          path: ["isfRecordGroupNumber"],
        });
      }
      if (!data.isfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Name is required",
          path: ["isfSectionName"],
        });
      }
      if (!data.isfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Number is required",
          path: ["isfSectionNumber"],
        });
      }
      if (!data.isfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Name is required",
          path: ["isfZoneName"],
        });
      }
      if (!data.isfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Number is required",
          path: ["isfZoneNumber"],
        });
      }
    }
  }

  const tmfFields = baseSchema
    .pick({
      isTMF: true,
      tmfRecordGroupName: true,
      tmfRecordGroupNumber: true,
      tmfSectionName: true,
      tmfSectionNumber: true,
      tmfZoneName: true,
      tmfZoneNumber: true,
    })
    .safeParse(input);

  if (tmfFields.success) {
    const data = tmfFields.data;
    if (data.isTMF) {
      if (!data.tmfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Name is required",
          path: ["tmfRecordGroupName"],
        });
      }
      if (!data.tmfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Number is required",
          path: ["tmfRecordGroupNumber"],
        });
      }
      if (!data.tmfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Name is required",
          path: ["tmfSectionName"],
        });
      }
      if (!data.tmfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Number is required",
          path: ["tmfSectionNumber"],
        });
      }
      if (!data.tmfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Name is required",
          path: ["tmfZoneName"],
        });
      }
      if (!data.tmfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Number is required",
          path: ["tmfZoneNumber"],
        });
      }
    }
  }

  const belongsFields = baseSchema
    .pick({
      isTMF: true,
      isISF: true,
    })
    .safeParse(input);

  if (belongsFields.success) {
    const data = belongsFields.data;
    if (!data.isTMF && !data.isISF) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "TMF or ISF is required",
        path: ["isTMF", "isISF"],
      });
    }
  }

  return input;
}, baseSchema);

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  category?: ArtifactCategory | null;
  versionId: string;
}

export const CategoryModal = ({
  isOpen,
  onClose,
  category,
  versionId,
}: CategoryModalProps) => {
  const isEditing = !!category;
  const { mutate: createCategory, isPending: isCreating } =
    useCreateArtifactCategory();
  const { mutate: updateCategory, isPending: isUpdating } =
    useUpdateArtifactCategory();

  const initialValues = {
    tmfRefModelId: category?.tmfRefModelId || "",
    isfRefModelId: category?.isfRefModelId || "",
    categoryVersionId: versionId || "",
    tmfZoneNumber: category?.tmfZoneNumber || "",
    tmfZoneName: category?.tmfZoneName || "",
    tmfSectionNumber: category?.tmfSectionNumber || "",
    tmfSectionName: category?.tmfSectionName || "",
    tmfRecordGroupNumber: category?.tmfRecordGroupNumber || "",
    tmfRecordGroupName: category?.tmfRecordGroupName || "",
    isfZoneNumber: category?.isfZoneNumber || "",
    isfZoneName: category?.isfZoneName || "",
    isfSectionNumber: category?.isfSectionNumber || "",
    isfSectionName: category?.isfSectionName || "",
    isfRecordGroupNumber: category?.isfRecordGroupNumber || "",
    isfRecordGroupName: category?.isfRecordGroupName || "",
    recordType: category?.recordType || "",
    alternativeNames: category?.alternativeNames || "",
    description: category?.description || "",
    requiresSignature: category?.requiresSignature || false,
    expires: category?.expires || false,
    inspectableRecord: category?.inspectableRecord || false,
    includesPHI: category?.includesPHI || false,
    tmfCore: category?.tmfCore || "",
    isTMF: category?.isTMF || false,
    isISF: category?.isISF || false,
    iitStudyArtifacts: category?.iitStudyArtifacts || "recommended",
    isActive: isEditing ? category?.isActive : true,
    origin: category?.origin ?? undefined,
  } as const;

  const formMethods = useForm<CategoryFormData>({
    mode: "onChange",
    resolver: zodResolver(categorySchema),
    defaultValues: initialValues,
  });

  const { handleSubmit } = formMethods;

  useEffect(() => {
    const triggerFieldValidation = (fields: readonly string[]) => {
      fields.forEach((field) =>
        formMethods.trigger(field as keyof CategoryFormData),
      );
    };

    const { unsubscribe } = formMethods.watch((_, { name }) => {
      if (name === "isTMF" || name === "isISF") {
        triggerFieldValidation(ISF_FIELDS);
        triggerFieldValidation(TMF_FIELDS);
      }
    });
    return () => unsubscribe();
  }, [formMethods]);

  async function onSubmit(data: CategoryFormData) {
    const payload = {
      ...data,
      categoryVersionId: versionId,
      origin: data.origin || undefined,
      tmfCore: data.tmfCore || undefined,
      iitStudyArtifacts: data.iitStudyArtifacts || undefined,
    };

    if (isEditing && category) {
      updateCategory(
        { id: category.id, payload },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    } else {
      createCategory(payload, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  }

  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      size="4xl"
      theme={{
        ...theme.modal,
        content: {
          ...theme.modal.content,
          inner: cn(theme.modal.content.inner, "overflow-hidden"),
        },
      }}
    >
      <ModalHeader>
        {isEditing ? "Edit Artifact Category" : "Add Artifact Category"}
      </ModalHeader>

      <FormProvider {...formMethods}>
        <form onSubmit={handleSubmit(onSubmit)} className="overflow-y-auto">
          <ModalBody>
            <div className="grid gap-4 sm:grid-cols-2">
              {/* Reference Models Section */}
              <div className="flex flex-col gap-4 sm:col-span-2 sm:flex-row">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="tmfRefModelId">TMF Ref Model</Label>
                  <LazySelect
                    name="tmfRefModelId"
                    placeholder="Enter TMF ref model..."
                    useInfiniteQuery={useInfiniteTMFRefModels}
                    getOptionLabel={(option) => option.tmfRefModel}
                    getOptionValue={(option) => option.id}
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <Label htmlFor="isfRefModelId">ISF Ref Model</Label>
                  <LazySelect
                    name="isfRefModelId"
                    placeholder="Enter ISF ref model..."
                    useInfiniteQuery={useInfiniteISFRefModels}
                    getOptionLabel={(option) => option.isfRefModel}
                    getOptionValue={(option) => option.id}
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <Label htmlFor="categoryVersionId">Category Version</Label>
                  <LazySelect
                    name="categoryVersionId"
                    placeholder="Select a version..."
                    useInfiniteQuery={useInfiniteCategoryVersion}
                    getOptionLabel={(option) => option.version.toString()}
                    getOptionValue={(option) => option.id}
                  />
                </div>
              </div>

              {/* TMF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  TMF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="tmfZoneNumber">TMF Zone Number</Label>
                    <Input
                      name="tmfZoneNumber"
                      placeholder="Enter TMF zone number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tmfZoneName">TMF Zone Name</Label>
                    <Input
                      name="tmfZoneName"
                      placeholder="Enter TMF zone name..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tmfSectionNumber">TMF Section Number</Label>
                    <Input
                      name="tmfSectionNumber"
                      placeholder="Enter TMF section number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tmfSectionName">TMF Section Name</Label>
                    <Input
                      name="tmfSectionName"
                      placeholder="Enter TMF section name..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tmfRecordGroupNumber">
                      TMF Record Group Number
                    </Label>
                    <Input
                      name="tmfRecordGroupNumber"
                      placeholder="Enter TMF record group number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tmfRecordGroupName">
                      TMF Record Group Name
                    </Label>
                    <Input
                      name="tmfRecordGroupName"
                      placeholder="Enter TMF record group name..."
                    />
                  </div>
                </div>
              </div>

              {/* ISF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  ISF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="isfZoneNumber">ISF Zone Number</Label>
                    <Input
                      name="isfZoneNumber"
                      placeholder="Enter ISF zone number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isfZoneName">ISF Zone Name</Label>
                    <Input
                      name="isfZoneName"
                      placeholder="Enter ISF zone name..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isfSectionNumber">ISF Section Number</Label>
                    <Input
                      name="isfSectionNumber"
                      placeholder="Enter ISF section number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isfSectionName">ISF Section Name</Label>
                    <Input
                      name="isfSectionName"
                      placeholder="Enter ISF section name..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isfRecordGroupNumber">
                      ISF Record Group Number
                    </Label>
                    <Input
                      name="isfRecordGroupNumber"
                      placeholder="Enter ISF record group number..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isfRecordGroupName">
                      ISF Record Group Name
                    </Label>
                    <Input
                      name="isfRecordGroupName"
                      placeholder="Enter ISF record group name..."
                    />
                  </div>
                </div>
              </div>
