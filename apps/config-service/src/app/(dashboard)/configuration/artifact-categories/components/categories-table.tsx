"use client";

import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Tooltip } from "flowbite-react";
import { BiHealth } from "react-icons/bi";
import { BsCalendar2XFill } from "react-icons/bs";
import {
  FaFolderOpen,
  FaMagnifyingGlass,
  FaRegFolderOpen,
} from "react-icons/fa6";
import { HiPencil, HiTrash } from "react-icons/hi";
import { PiSignature } from "react-icons/pi";

import { ActiveStatusBadge, TableData } from "@/components";
import { Button } from "@/components/ui/button";
import {
  artifactCategories,
  ArtifactCategory,
} from "@/lib/apis/artifact-categories";

import { useDeleteArtifactCategory } from "../hooks/use-artifact-category-mutations";

interface CategoriesTableProps {
  versionId: string;
  onEditCategory: (category: ArtifactCategory) => void;
}

export const CategoriesTable = ({
  versionId,
  onEditCategory,
}: CategoriesTableProps) => {
  const { mutate: deleteCategory } = useDeleteArtifactCategory();

  const { data, isLoading, error, isError } = useQuery({
    queryKey: ["artifact-categories", versionId],
    queryFn: () =>
      artifactCategories.list({
        version: versionId,
        isActive: true,
        limit: 100,
      }),
    enabled: !!versionId,
    retry: 2,
    retryDelay: 1000,
  });

  const handleDeleteCategory = (id: string) => {
    if (confirm("Are you sure you want to delete this artifact category?")) {
      deleteCategory(id);
    }
  };

  const columns: ColumnDef<ArtifactCategory>[] = [
    {
      header: "Type",
      accessorKey: "type",
      id: "type",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <div className="flex flex-col gap-1">
            <Tooltip content={`TMF: ${data.tmfRefModel?.tmfRefModel || "N/A"}`}>
              <FaRegFolderOpen size={20} className="text-blue-600" />
            </Tooltip>
            <Tooltip content={`ISF: ${data.isfRefModel?.isfRefModel || "N/A"}`}>
              <FaFolderOpen size={20} className="text-green-600" />
            </Tooltip>
          </div>
        );
      },
    },
    {
      header: "Document Number",
      accessorKey: "documentNumber",
      id: "documentNumber",
      cell: ({ row }) => {
        const data = row.original;
        const tmfNumber =
          data.tmfZoneNumber &&
          data.tmfSectionNumber &&
          data.tmfRecordGroupNumber
            ? `${data.tmfZoneNumber}.${data.tmfSectionNumber}.${data.tmfRecordGroupNumber}`
            : null;
        const isfNumber =
          data.isfZoneNumber &&
          data.isfSectionNumber &&
          data.isfRecordGroupNumber
            ? `${data.isfZoneNumber}.${data.isfSectionNumber}.${data.isfRecordGroupNumber}`
            : null;

        return (
          <button
            className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
            onClick={() => onEditCategory(data)}
          >
            {tmfNumber && <span>{tmfNumber}</span>}
            {isfNumber && <span>{isfNumber}</span>}
          </button>
        );
      },
    },
    {
      header: "Record Group",
      accessorKey: "recordGroup",
      id: "recordGroup",
      cell: ({ row }) => {
        const data = row.original;
        const tmfGroup =
          data.tmfZoneName && data.tmfSectionName && data.tmfRecordGroupName
            ? `${data.tmfZoneName} / ${data.tmfSectionName} / ${data.tmfRecordGroupName}`
            : null;
        const isfGroup =
          data.isfZoneName && data.isfSectionName && data.isfRecordGroupName
            ? `${data.isfZoneName} / ${data.isfSectionName} / ${data.isfRecordGroupName}`
            : null;

        return (
          <Tooltip content={data.description || "No description available"}>
            <div className="flex cursor-help flex-col">
              {tmfGroup && <span className="text-sm">{tmfGroup}</span>}
              {isfGroup && <span className="text-sm">{isfGroup}</span>}
            </div>
          </Tooltip>
        );
      },
    },
    {
      header: "Record Type",
      accessorKey: "recordType",
      id: "recordType",
      cell: ({ row }) => {
        const data = row.original;
        const alternativeNames = data.alternativeNames;
        const tooltipContent = alternativeNames
          ? `${alternativeNames}`
          : "No alternative names";

        return (
          <Tooltip content={tooltipContent}>
            <span className="cursor-help">{data.recordType}</span>
          </Tooltip>
        );
      },
    },
    {
      header: "Properties",
      id: "properties",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <div className="flex items-center gap-2">
            {data.requiresSignature && (
              <Tooltip content="Requires Signature">
                <PiSignature size={20} className="text-blue-600" />
              </Tooltip>
            )}
            {data.expires && (
              <Tooltip content="Expires">
                <BsCalendar2XFill size={20} className="text-orange-600" />
              </Tooltip>
            )}
            {data.inspectableRecord && (
              <Tooltip content="Inspectable">
                <FaMagnifyingGlass size={20} className="text-purple-600" />
              </Tooltip>
            )}
            {data.includesPHI && (
              <Tooltip content="PHI">
                <BiHealth size={20} className="text-red-600" />
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      header: "Status",
      cell: ({ row }) => {
        return <ActiveStatusBadge isActive={row.original.isActive} />;
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const category = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => onEditCategory(category)}
              className="h-8 w-8 p-0"
            >
              <HiPencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={() => handleDeleteCategory(category.id)}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            >
              <HiTrash className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  if (isError) {
    return (
      <div className="rounded-lg bg-red-50 p-8 text-center">
        <p className="text-red-600">
          Failed to load categories: {error?.message || "Unknown error"}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-700 underline hover:text-red-800"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <TableData
        data={data?.results || []}
        columns={columns}
        isLoading={isLoading}
        enableSorting={true}
      />

      {data?.metadata && (
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>
            Showing {data.results.length} of {data.metadata.totalCount}{" "}
            categories
          </span>
        </div>
      )}
    </div>
  );
};
