"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
} from "@/components";
import { Input } from "@/components/ui/form/input";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import {
  useCreateCategoryVersion,
  useUpdateCategoryVersion,
} from "../hooks/use-category-version-mutations";

const versionSchema = z.object({
  version: z.number().min(1, "Version must be at least 1"),
  effectiveDate: z.string().min(1, "Effective date is required"),
  notes: z.string().optional(),
});

type VersionFormData = z.infer<typeof versionSchema>;

interface CategoryVersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  version?: CategoryVersion | null;
}

export const CategoryVersionModal = ({
  isOpen,
  onClose,
  version,
}: CategoryVersionModalProps) => {
  const isEditing = !!version;
  const { mutate: createVersion, isPending: isCreating } =
    useCreateCategoryVersion();
  const { mutate: updateVersion, isPending: isUpdating } =
    useUpdateCategoryVersion();

  const methods = useForm<VersionFormData>({
    resolver: zodResolver(versionSchema),
    defaultValues: {
      version: 1,
      effectiveDate: new Date().toISOString().split("T")[0],
      notes: "",
    },
  });

  const { handleSubmit, reset } = methods;

  // Reset form when version changes
  useEffect(() => {
    if (version) {
      reset({
        version: version.version,
        effectiveDate: new Date(version.effectiveDate)
          .toISOString()
          .split("T")[0],
        notes: version.notes || "",
      });
    } else {
      reset({
        version: 1,
        effectiveDate: new Date().toISOString().split("T")[0],
        notes: "",
      });
    }
  }, [version, reset]);

  const onSubmit = (data: VersionFormData) => {
    if (isEditing && version) {
      updateVersion(
        {
          id: version.id,
          version: data.version,
          effectiveDate: data.effectiveDate,
          notes: data.notes || null,
        },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    } else {
      createVersion(
        {
          version: data.version,
          effectiveDate: data.effectiveDate,
          notes: data.notes || null,
        },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <ModalHeader>
        {isEditing ? "Edit Category Version" : "Add Category Version"}
      </ModalHeader>

      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalBody>
            <div className="space-y-4">
              <Input
                name="version"
                label="Version Number"
                type="number"
                placeholder="Enter version number"
                required
              />

              <Input
                name="effectiveDate"
                label="Effective Date"
                type="date"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-900">
                  Notes
                </label>
                <textarea
                  {...methods.register("notes")}
                  className="w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm focus:border-blue-500 focus:ring-blue-500"
                  rows={4}
                  placeholder="Enter any notes about this version..."
                />
              </div>
            </div>
          </ModalBody>

          <ModalFooter>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={onClose} type="button">
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                isLoading={isCreating || isUpdating}
              >
                {isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
