"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { theme } from "flowbite-react";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
} from "@/components";
import { Checkbox, Input, Label, Select, Textarea } from "@/components/ui/form";
import { LazySelect } from "@/components/ui/lazy-select";
import { ArtifactCategory } from "@/lib/apis/artifact-categories";
import { cn } from "@/lib/utils";
import { capitalize } from "@/lib/utils/string";

import {
  useCreateArtifactCategory,
  useCreateArtifactCategory,
  useUpdateArtifactCategory,
} from "../hooks/use-artifact-category-mutations";
import { useInfiniteCategoryVersion } from "../hooks/use-infinite-category-versions";
import { useInfiniteISFRefModels } from "../hooks/use-infinite-isf-ref-models";
import { useInfiniteTMFRefModels } from "../hooks/use-infinite-tmf-ref-models";

export const IIT_STUDY_ARTIFACTS = [
  "recommended",
  "dependent",
  "mandatory",
] as const;

export const TMF_CORE = ["recommended", "core"] as const;

export const ORIGINS = [
  { label: "TMF", value: "to_TMF" },
  { label: "ISF", value: "to_ISF" },
] as const;

const TMF_FIELDS = [
  "tmfZoneNumber",
  "tmfZoneName",
  "tmfSectionNumber",
  "tmfSectionName",
  "tmfRecordGroupNumber",
  "tmfRecordGroupName",
] as const;

const ISF_FIELDS = [
  "isfZoneNumber",
  "isfZoneName",
  "isfSectionNumber",
  "isfSectionName",
  "isfRecordGroupNumber",
  "isfRecordGroupName",
] as const;

export const baseSchema = z.object({
  tmfRefModelId: z
    .string({ required_error: "TMF ref model is required" })
    .min(1, "TMF ref model is required"),
  isfRefModelId: z
    .string({ required_error: "ISF ref model is required" })
    .min(1, "ISF ref model is required"),
  categoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),

  tmfZoneNumber: z.string().nullable().optional(),
  tmfZoneName: z.string().nullable().optional(),
  tmfSectionNumber: z.string().nullable().optional(),
  tmfSectionName: z.string().nullable().optional(),
  tmfRecordGroupNumber: z.string().nullable().optional(),
  tmfRecordGroupName: z.string().nullable().optional(),

  isfZoneNumber: z.string().nullable().optional(),
  isfZoneName: z.string().nullable().optional(),
  isfSectionNumber: z.string().nullable().optional(),
  isfSectionName: z.string().nullable().optional(),
  isfRecordGroupNumber: z.string().nullable().optional(),
  isfRecordGroupName: z.string().nullable().optional(),

  recordType: z
    .string({
      invalid_type_error: "Record type is required",
      required_error: "Record type is required",
    })
    .min(1, "Record type is required"),
  alternativeNames: z.string().nullable().optional(),

  description: z.string().nullable().optional(),
  requiresSignature: z.boolean().nullable().optional(),
  expires: z.boolean().nullable().optional(),
  inspectableRecord: z.boolean().nullable().optional(),
  includesPHI: z.boolean().nullable().optional(),
  origin: z.enum(["to_ISF", "to_TMF", ""]).nullable().optional(),
  isTMF: z.boolean().default(false),
  isISF: z.boolean().default(false),
  tmfCore: z
    .enum([...TMF_CORE, ""])
    .nullable()
    .optional(),
  iitStudyArtifacts: z.enum(IIT_STUDY_ARTIFACTS, {
    errorMap: () => ({
      message: "Investigator Initiated Study Artifacts is required",
    }),
  }),
  isActive: z.boolean().default(true),
});

const categorySchema = z.preprocess((input, ctx) => {
  const isfFields = baseSchema
    .pick({
      isISF: true,
      isfRecordGroupName: true,
      isfRecordGroupNumber: true,
      isfSectionName: true,
      isfSectionNumber: true,
      isfZoneName: true,
      isfZoneNumber: true,
    })
    .safeParse(input);
  if (isfFields.success) {
    const data = isfFields.data;
    if (data.isISF) {
      if (!data.isfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Name is required",
          path: ["isfRecordGroupName"],
        });
      }
      if (!data.isfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Number is required",
          path: ["isfRecordGroupNumber"],
        });
      }
      if (!data.isfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Name is required",
          path: ["isfSectionName"],
        });
      }
      if (!data.isfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Number is required",
          path: ["isfSectionNumber"],
        });
      }
      if (!data.isfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Name is required",
          path: ["isfZoneName"],
        });
      }
      if (!data.isfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Number is required",
          path: ["isfZoneNumber"],
        });
      }
    }
  }

  const tmfFields = baseSchema
    .pick({
      isTMF: true,
      tmfRecordGroupName: true,
      tmfRecordGroupNumber: true,
      tmfSectionName: true,
      tmfSectionNumber: true,
      tmfZoneName: true,
      tmfZoneNumber: true,
    })
    .safeParse(input);

  if (tmfFields.success) {
    const data = tmfFields.data;
    if (data.isTMF) {
      if (!data.tmfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Name is required",
          path: ["tmfRecordGroupName"],
        });
      }
      if (!data.tmfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Number is required",
          path: ["tmfRecordGroupNumber"],
        });
      }
      if (!data.tmfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Name is required",
          path: ["tmfSectionName"],
        });
      }
      if (!data.tmfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Number is required",
          path: ["tmfSectionNumber"],
        });
      }
      if (!data.tmfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Name is required",
          path: ["tmfZoneName"],
        });
      }
      if (!data.tmfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Number is required",
          path: ["tmfZoneNumber"],
        });
      }
    }
  }

  const belongsFields = baseSchema
    .pick({
      isTMF: true,
      isISF: true,
    })
    .safeParse(input);

  if (belongsFields.success) {
    const data = belongsFields.data;
    if (!data.isTMF && !data.isISF) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "TMF or ISF is required",
        path: ["isTMF", "isISF"],
      });
    }
  }

  return input;
}, baseSchema);

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  category?: ArtifactCategory | null;
  versionId: string;
}

export const CategoryModal = ({
  isOpen,
  onClose,
  category,
  versionId,
}: CategoryModalProps) => {
  const isEditing = !!category;
  const { mutate: createCategory, isPending: isCreating } =
    useCreateArtifactCategory();
  const { mutate: updateCategory, isPending: isUpdating } =
    useUpdateArtifactCategory();

  const methods = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      recordType: "",
      tmfZoneNumber: "",
      tmfZoneName: "",
      tmfSectionNumber: "",
      tmfSectionName: "",
      tmfRecordGroupNumber: "",
      tmfRecordGroupName: "",
      isfZoneNumber: "",
      isfZoneName: "",
      isfSectionNumber: "",
      isfSectionName: "",
      isfRecordGroupNumber: "",
      isfRecordGroupName: "",
      alternativeNames: "",
      description: "",
      requiresSignature: false,
      expires: false,
      inspectableRecord: false,
      includesPHI: false,
      isTMF: false,
      isISF: false,
      origin: null,
      tmfCore: null,
      iitStudyArtifacts: "Not Applicable",
      isActive: true,
      tmfRefModelId: "default-tmf-ref", // You'll need to implement proper reference models
      isfRefModelId: "default-isf-ref", // You'll need to implement proper reference models
    },
  });

  const { handleSubmit, reset } = methods;

  // Reset form when category changes
  useEffect(() => {
    if (category) {
      reset({
        recordType: category.recordType || "",
        tmfZoneNumber: category.tmfZoneNumber || "",
        tmfZoneName: category.tmfZoneName || "",
        tmfSectionNumber: category.tmfSectionNumber || "",
        tmfSectionName: category.tmfSectionName || "",
        tmfRecordGroupNumber: category.tmfRecordGroupNumber || "",
        tmfRecordGroupName: category.tmfRecordGroupName || "",
        isfZoneNumber: category.isfZoneNumber || "",
        isfZoneName: category.isfZoneName || "",
        isfSectionNumber: category.isfSectionNumber || "",
        isfSectionName: category.isfSectionName || "",
        isfRecordGroupNumber: category.isfRecordGroupNumber || "",
        isfRecordGroupName: category.isfRecordGroupName || "",
        alternativeNames: category.alternativeNames || "",
        description: category.description || "",
        requiresSignature: category.requiresSignature || false,
        expires: category.expires || false,
        inspectableRecord: category.inspectableRecord || false,
        includesPHI: category.includesPHI || false,
        isTMF: category.isTMF,
        isISF: category.isISF,
        origin: category.origin || null,
        tmfCore: category.tmfCore || null,
        iitStudyArtifacts: category.iitStudyArtifacts || "Not Applicable",
        isActive: category.isActive,
        tmfRefModelId: category.tmfRefModelId,
        isfRefModelId: category.isfRefModelId,
      });
    } else {
      reset({
        recordType: "",
        tmfZoneNumber: "",
        tmfZoneName: "",
        tmfSectionNumber: "",
        tmfSectionName: "",
        tmfRecordGroupNumber: "",
        tmfRecordGroupName: "",
        isfZoneNumber: "",
        isfZoneName: "",
        isfSectionNumber: "",
        isfSectionName: "",
        isfRecordGroupNumber: "",
        isfRecordGroupName: "",
        alternativeNames: "",
        description: "",
        requiresSignature: false,
        expires: false,
        inspectableRecord: false,
        includesPHI: false,
        isTMF: false,
        isISF: false,
        origin: null,
        tmfCore: null,
        iitStudyArtifacts: "Not Applicable",
        isActive: true,
        tmfRefModelId: "default-tmf-ref",
        isfRefModelId: "default-isf-ref",
      });
    }
  }, [category, reset]);

  const onSubmit = (data: CategoryFormData) => {
    const payload = {
      ...data,
      categoryVersionId: versionId,
    };

    if (isEditing && category) {
      updateCategory(
        { id: category.id, payload },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    } else {
      createCategory(payload, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      size="4xl"
      theme={{
        ...theme.modal,
        content: {
          ...theme.modal.content,
          inner: cn(theme.modal.content.inner, "overflow-hidden"),
        },
      }}
    >
      <ModalHeader>
        {isEditing ? "Edit Artifact Category" : "Add Artifact Category"}
      </ModalHeader>

      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className="overflow-y-auto">
          <ModalBody>
            <div className="grid gap-4 sm:grid-cols-2">
              {/* Reference Models Section */}
              <div className="flex flex-col gap-4 sm:col-span-2 sm:flex-row">
                <div className="flex-1 space-y-2">
                  <Input
                    name="tmfRefModelId"
                    label="TMF Ref Model"
                    placeholder="Enter TMF ref model..."
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <Input
                    name="isfRefModelId"
                    label="ISF Ref Model"
                    placeholder="Enter ISF ref model..."
                  />
                </div>
              </div>

              {/* TMF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  TMF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <Input
                    name="tmfZoneNumber"
                    label="TMF Zone Number"
                    placeholder="Enter TMF zone number..."
                  />
                  <Input
                    name="tmfZoneName"
                    label="TMF Zone Name"
                    placeholder="Enter TMF zone name..."
                  />
                  <Input
                    name="tmfSectionNumber"
                    label="TMF Section Number"
                    placeholder="Enter TMF section number..."
                  />
                  <Input
                    name="tmfSectionName"
                    label="TMF Section Name"
                    placeholder="Enter TMF section name..."
                  />
                  <Input
                    name="tmfRecordGroupNumber"
                    label="TMF Record Group Number"
                    placeholder="Enter TMF record group number..."
                  />
                  <Input
                    name="tmfRecordGroupName"
                    label="TMF Record Group Name"
                    placeholder="Enter TMF record group name..."
                  />
                </div>
              </div>

              {/* ISF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  ISF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <Input
                    name="isfZoneNumber"
                    label="ISF Zone Number"
                    placeholder="Enter ISF zone number..."
                  />
                  <Input
                    name="isfZoneName"
                    label="ISF Zone Name"
                    placeholder="Enter ISF zone name..."
                  />
                  <Input
                    name="isfSectionNumber"
                    label="ISF Section Number"
                    placeholder="Enter ISF section number..."
                  />
                  <Input
                    name="isfSectionName"
                    label="ISF Section Name"
                    placeholder="Enter ISF section name..."
                  />
                  <Input
                    name="isfRecordGroupNumber"
                    label="ISF Record Group Number"
                    placeholder="Enter ISF record group number..."
                  />
                  <Input
                    name="isfRecordGroupName"
                    label="ISF Record Group Name"
                    placeholder="Enter ISF record group name..."
                  />
                </div>
              </div>

              {/* Basic Information Fields */}
              <Input
                name="recordType"
                label="Record Type"
                placeholder="Enter record type..."
              />
              <Input
                name="alternativeNames"
                label="Alternative Names"
                placeholder="Enter alternative names..."
              />

              <div className="space-y-2 sm:col-span-2">
                <Input
                  name="description"
                  label="Description"
                  placeholder="Enter description..."
                />
              </div>

              {/* Belongs Section */}
              <div className="flex flex-col space-y-2">
                <span className="text-sm font-medium text-gray-900">
                  Belongs
                </span>
                <div className="flex flex-1 gap-4">
                  <div className="flex flex-1 items-center gap-2">
                    <span className="text-sm font-medium uppercase text-gray-900">
                      TMF
                    </span>
                    <Checkbox name="isTMF" />
                  </div>
                  <div className="flex flex-1 items-center gap-2">
                    <span className="text-sm font-medium uppercase text-gray-900">
                      ISF
                    </span>
                    <Checkbox name="isISF" />
                  </div>
                </div>
              </div>

              <Select
                name="origin"
                label="Origin"
                placeholder="Select origin..."
                options={ORIGINS.map((o) => ({
                  label: o.label,
                  value: o.value,
                }))}
              />

              <Select
                name="tmfCore"
                label="TMF Core"
                placeholder="Select TMF core..."
                options={TMF_CORE.map((c) => ({ label: c, value: c }))}
              />

              <Select
                name="iitStudyArtifacts"
                label="Investigator Initiated Study Artifacts"
                placeholder="Select IIT study artifacts..."
                options={IIT_STUDY_ARTIFACTS.map((a) => ({
                  label: a,
                  value: a,
                }))}
              />

              <div className="flex items-center gap-2">
                <Checkbox name="isActive" />
                <span className="text-sm font-medium text-gray-900">
                  Active
                </span>
              </div>

              {/* Additional Properties Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  Additional Properties
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <Checkbox name="requiresSignature" />
                    <span className="text-sm font-medium text-gray-900">
                      Requires Signature
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="expires" />
                    <span className="text-sm font-medium text-gray-900">
                      Expires
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="inspectableRecord" />
                    <span className="text-sm font-medium text-gray-900">
                      Inspectable Record
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="includesPHI" />
                    <span className="text-sm font-medium text-gray-900">
                      Includes PHI
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>

          <ModalFooter>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={onClose} type="button">
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                isLoading={isCreating || isUpdating}
              >
                {isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
