"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { theme } from "flowbite-react";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>eader,
} from "@/components";
import { Checkbox } from "@/components/ui/form/checkbox";
import { Input } from "@/components/ui/form/input";
import { Select } from "@/components/ui/form/select";
import {
  ArtifactCategory,
  IIT_STUDY_ARTIFACTS,
  ORIGINS,
  TMF_CORE,
} from "@/lib/apis/artifact-categories";
import { cn } from "@/lib/utils";

import {
  useCreateArtifactCategory,
  useUpdateArtifactCategory,
} from "../hooks/use-artifact-category-mutations";

const categorySchema = z.object({
  recordType: z.string().min(1, "Record type is required"),
  tmfZoneNumber: z.string().optional(),
  tmfZoneName: z.string().optional(),
  tmfSectionNumber: z.string().optional(),
  tmfSectionName: z.string().optional(),
  tmfRecordGroupNumber: z.string().optional(),
  tmfRecordGroupName: z.string().optional(),
  isfZoneNumber: z.string().optional(),
  isfZoneName: z.string().optional(),
  isfSectionNumber: z.string().optional(),
  isfSectionName: z.string().optional(),
  isfRecordGroupNumber: z.string().optional(),
  isfRecordGroupName: z.string().optional(),
  alternativeNames: z.string().optional(),
  description: z.string().optional(),
  requiresSignature: z.boolean().optional(),
  expires: z.boolean().optional(),
  inspectableRecord: z.boolean().optional(),
  includesPHI: z.boolean().optional(),
  isTMF: z.boolean(),
  isISF: z.boolean(),
  origin: z.enum(["DIA", "ICH", "FDA", "EMA", "OTHER"]).optional().nullable(),
  tmfCore: z.enum(["Core", "Recommended"]).optional().nullable(),
  iitStudyArtifacts: z
    .enum(["Required", "Optional", "Not Applicable"])
    .optional(),
  isActive: z.boolean(),
  tmfRefModelId: z.string().min(1, "TMF reference model is required"),
  isfRefModelId: z.string().min(1, "ISF reference model is required"),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  category?: ArtifactCategory | null;
  versionId: string;
}

export const CategoryModal = ({
  isOpen,
  onClose,
  category,
  versionId,
}: CategoryModalProps) => {
  const isEditing = !!category;
  const { mutate: createCategory, isPending: isCreating } =
    useCreateArtifactCategory();
  const { mutate: updateCategory, isPending: isUpdating } =
    useUpdateArtifactCategory();

  const methods = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      recordType: "",
      tmfZoneNumber: "",
      tmfZoneName: "",
      tmfSectionNumber: "",
      tmfSectionName: "",
      tmfRecordGroupNumber: "",
      tmfRecordGroupName: "",
      isfZoneNumber: "",
      isfZoneName: "",
      isfSectionNumber: "",
      isfSectionName: "",
      isfRecordGroupNumber: "",
      isfRecordGroupName: "",
      alternativeNames: "",
      description: "",
      requiresSignature: false,
      expires: false,
      inspectableRecord: false,
      includesPHI: false,
      isTMF: false,
      isISF: false,
      origin: null,
      tmfCore: null,
      iitStudyArtifacts: "Not Applicable",
      isActive: true,
      tmfRefModelId: "default-tmf-ref", // You'll need to implement proper reference models
      isfRefModelId: "default-isf-ref", // You'll need to implement proper reference models
    },
  });

  const { handleSubmit, reset } = methods;

  // Reset form when category changes
  useEffect(() => {
    if (category) {
      reset({
        recordType: category.recordType || "",
        tmfZoneNumber: category.tmfZoneNumber || "",
        tmfZoneName: category.tmfZoneName || "",
        tmfSectionNumber: category.tmfSectionNumber || "",
        tmfSectionName: category.tmfSectionName || "",
        tmfRecordGroupNumber: category.tmfRecordGroupNumber || "",
        tmfRecordGroupName: category.tmfRecordGroupName || "",
        isfZoneNumber: category.isfZoneNumber || "",
        isfZoneName: category.isfZoneName || "",
        isfSectionNumber: category.isfSectionNumber || "",
        isfSectionName: category.isfSectionName || "",
        isfRecordGroupNumber: category.isfRecordGroupNumber || "",
        isfRecordGroupName: category.isfRecordGroupName || "",
        alternativeNames: category.alternativeNames || "",
        description: category.description || "",
        requiresSignature: category.requiresSignature || false,
        expires: category.expires || false,
        inspectableRecord: category.inspectableRecord || false,
        includesPHI: category.includesPHI || false,
        isTMF: category.isTMF,
        isISF: category.isISF,
        origin: category.origin || null,
        tmfCore: category.tmfCore || null,
        iitStudyArtifacts: category.iitStudyArtifacts || "Not Applicable",
        isActive: category.isActive,
        tmfRefModelId: category.tmfRefModelId,
        isfRefModelId: category.isfRefModelId,
      });
    } else {
      reset({
        recordType: "",
        tmfZoneNumber: "",
        tmfZoneName: "",
        tmfSectionNumber: "",
        tmfSectionName: "",
        tmfRecordGroupNumber: "",
        tmfRecordGroupName: "",
        isfZoneNumber: "",
        isfZoneName: "",
        isfSectionNumber: "",
        isfSectionName: "",
        isfRecordGroupNumber: "",
        isfRecordGroupName: "",
        alternativeNames: "",
        description: "",
        requiresSignature: false,
        expires: false,
        inspectableRecord: false,
        includesPHI: false,
        isTMF: false,
        isISF: false,
        origin: null,
        tmfCore: null,
        iitStudyArtifacts: "Not Applicable",
        isActive: true,
        tmfRefModelId: "default-tmf-ref",
        isfRefModelId: "default-isf-ref",
      });
    }
  }, [category, reset]);

  const onSubmit = (data: CategoryFormData) => {
    const payload = {
      ...data,
      categoryVersionId: versionId,
    };

    if (isEditing && category) {
      updateCategory(
        { id: category.id, payload },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    } else {
      createCategory(payload, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  return (
    <Modal
      show={isOpen}
      onClose={onClose}
      size="4xl"
      theme={{
        ...theme.modal,
        content: {
          ...theme.modal.content,
          inner: cn(theme.modal.content.inner, "overflow-hidden"),
        },
      }}
    >
      <ModalHeader>
        {isEditing ? "Edit Artifact Category" : "Add Artifact Category"}
      </ModalHeader>

      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className="overflow-y-auto">
          <ModalBody>
            <div className="grid gap-4 sm:grid-cols-2">
              {/* Reference Models Section */}
              <div className="flex flex-col gap-4 sm:col-span-2 sm:flex-row">
                <div className="flex-1 space-y-2">
                  <Input
                    name="tmfRefModelId"
                    label="TMF Ref Model"
                    placeholder="Enter TMF ref model..."
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <Input
                    name="isfRefModelId"
                    label="ISF Ref Model"
                    placeholder="Enter ISF ref model..."
                  />
                </div>
              </div>

              {/* TMF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  TMF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <Input
                    name="tmfZoneNumber"
                    label="TMF Zone Number"
                    placeholder="Enter TMF zone number..."
                  />
                  <Input
                    name="tmfZoneName"
                    label="TMF Zone Name"
                    placeholder="Enter TMF zone name..."
                  />
                  <Input
                    name="tmfSectionNumber"
                    label="TMF Section Number"
                    placeholder="Enter TMF section number..."
                  />
                  <Input
                    name="tmfSectionName"
                    label="TMF Section Name"
                    placeholder="Enter TMF section name..."
                  />
                  <Input
                    name="tmfRecordGroupNumber"
                    label="TMF Record Group Number"
                    placeholder="Enter TMF record group number..."
                  />
                  <Input
                    name="tmfRecordGroupName"
                    label="TMF Record Group Name"
                    placeholder="Enter TMF record group name..."
                  />
                </div>
              </div>

              {/* ISF Fields Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  ISF Fields
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <Input
                    name="isfZoneNumber"
                    label="ISF Zone Number"
                    placeholder="Enter ISF zone number..."
                  />
                  <Input
                    name="isfZoneName"
                    label="ISF Zone Name"
                    placeholder="Enter ISF zone name..."
                  />
                  <Input
                    name="isfSectionNumber"
                    label="ISF Section Number"
                    placeholder="Enter ISF section number..."
                  />
                  <Input
                    name="isfSectionName"
                    label="ISF Section Name"
                    placeholder="Enter ISF section name..."
                  />
                  <Input
                    name="isfRecordGroupNumber"
                    label="ISF Record Group Number"
                    placeholder="Enter ISF record group number..."
                  />
                  <Input
                    name="isfRecordGroupName"
                    label="ISF Record Group Name"
                    placeholder="Enter ISF record group name..."
                  />
                </div>
              </div>

              {/* Basic Information Fields */}
              <Input
                name="recordType"
                label="Record Type"
                placeholder="Enter record type..."
              />
              <Input
                name="alternativeNames"
                label="Alternative Names"
                placeholder="Enter alternative names..."
              />

              <div className="space-y-2 sm:col-span-2">
                <Input
                  name="description"
                  label="Description"
                  placeholder="Enter description..."
                />
              </div>

              {/* Belongs Section */}
              <div className="flex flex-col space-y-2">
                <span className="text-sm font-medium text-gray-900">
                  Belongs
                </span>
                <div className="flex flex-1 gap-4">
                  <div className="flex flex-1 items-center gap-2">
                    <span className="text-sm font-medium uppercase text-gray-900">
                      TMF
                    </span>
                    <Checkbox name="isTMF" />
                  </div>
                  <div className="flex flex-1 items-center gap-2">
                    <span className="text-sm font-medium uppercase text-gray-900">
                      ISF
                    </span>
                    <Checkbox name="isISF" />
                  </div>
                </div>
              </div>

              <Select
                name="origin"
                label="Origin"
                placeholder="Select origin..."
                options={ORIGINS.map((o) => ({
                  label: o.label,
                  value: o.value,
                }))}
              />

              <Select
                name="tmfCore"
                label="TMF Core"
                placeholder="Select TMF core..."
                options={TMF_CORE.map((c) => ({ label: c, value: c }))}
              />

              <Select
                name="iitStudyArtifacts"
                label="Investigator Initiated Study Artifacts"
                placeholder="Select IIT study artifacts..."
                options={IIT_STUDY_ARTIFACTS.map((a) => ({
                  label: a,
                  value: a,
                }))}
              />

              <div className="flex items-center gap-2">
                <Checkbox name="isActive" />
                <span className="text-sm font-medium text-gray-900">
                  Active
                </span>
              </div>

              {/* Additional Properties Section */}
              <div className="space-y-4 sm:col-span-2">
                <h3 className="text-lg font-medium text-gray-900">
                  Additional Properties
                </h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <Checkbox name="requiresSignature" />
                    <span className="text-sm font-medium text-gray-900">
                      Requires Signature
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="expires" />
                    <span className="text-sm font-medium text-gray-900">
                      Expires
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="inspectableRecord" />
                    <span className="text-sm font-medium text-gray-900">
                      Inspectable Record
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox name="includesPHI" />
                    <span className="text-sm font-medium text-gray-900">
                      Includes PHI
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>

          <ModalFooter>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={onClose} type="button">
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                isLoading={isCreating || isUpdating}
              >
                {isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
