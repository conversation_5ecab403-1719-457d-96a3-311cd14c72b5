import { useInfiniteQuery } from "@tanstack/react-query";

import { artifactCategories } from "@/lib/apis/artifact-categories";

export const USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY =
  "infinite-category-versions";

export const useInfiniteCategoryVersions = (
  search: string,
  initialPageSize = 20,
) => {
  return useInfiniteQuery({
    queryKey: [USE_INFINITE_CATEGORY_VERSIONS_QUERY_KEY, search],
    queryFn: ({ pageParam = 1 }) => {
      const params: any = {
        page: pageParam,
        limit: initialPageSize,
        orderBy: "version",
        orderDirection: "desc",
      };

      if (search && search.trim() !== "") {
        params.search = search;
      }

      return artifactCategories.getVersions(params);
    },
    getNextPageParam: (lastPage) => {
      if (
        lastPage &&
        lastPage.metadata &&
        lastPage.metadata.currentPage < lastPage.metadata.totalPages
      ) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
