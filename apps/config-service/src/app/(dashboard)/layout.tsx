import { SignedIn, SignedOut } from "@clerk/nextjs";
import type { PropsWithChildren } from "react";

import { DashboardWrapper } from "@/components/layout/dashboard-wrapper";

import {
  ClientAuthenticate,
  ClientSignInPageContent,
} from "./_client-components";

export default async function DashboardLayout({ children }: PropsWithChildren) {
  return (
    <>
      <SignedIn>
        <ClientAuthenticate>
          <DashboardWrapper>{children}</DashboardWrapper>
        </ClientAuthenticate>
      </SignedIn>
      <SignedOut>
        <ClientSignInPageContent />
      </SignedOut>
    </>
  );
}
