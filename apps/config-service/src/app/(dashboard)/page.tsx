import { LayoutContent } from "@/components";

export default function Index() {
  return (
    <LayoutContent>
      <div className="p-6">
        <h1 className="mb-4 text-3xl font-bold text-gray-900">
          Welcome to Config Service
        </h1>
        <div className="rounded-lg bg-white p-6 shadow">
          <h2 className="mb-3 text-xl font-semibold text-gray-800">
            Configuration Dashboard
          </h2>
          <p className="text-gray-600">
            Use the sidebar to navigate to different configuration sections:
          </p>
          <ul className="mt-4 space-y-2 text-sm text-gray-600">
            <li>
              • <strong>Recent Changes:</strong> View recent configuration
              updates
            </li>
            <li>
              • <strong>Sync Status:</strong> Monitor synchronization status
            </li>
            <li>
              • <strong>Configuration:</strong> Manage AI prompts, templates,
              and categories
            </li>
            <li>
              • <strong>Reference Models:</strong> Configure ISF and TMF
              reference models
            </li>
          </ul>
        </div>
      </div>
    </LayoutContent>
  );
}
