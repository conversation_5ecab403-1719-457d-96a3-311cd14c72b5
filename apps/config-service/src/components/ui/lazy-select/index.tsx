"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type {
  InfiniteData,
  UseInfiniteQueryResult,
} from "@tanstack/react-query";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { ChevronDown } from "lucide-react";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MdOutlineClear } from "react-icons/md";

import { cn } from "@/lib/utils";

const useDebounce = (value: string, delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

type PageData<T> = {
  results: T[];
  metadata: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    itemsPerPage: number;
  };
};

export type LazySelectProps<T> = Omit<FlowbiteSelectProps, "name"> & {
  name: string;
  shouldShowError?: boolean;
  params?: any[];
  useInfiniteQuery: (
    search: string,
    ...args: any[]
  ) => UseInfiniteQueryResult<InfiniteData<PageData<T>>, unknown>;
  getOptionLabel: (option: T) => string;
  getOptionValue: (option: T) => string;
  placeholder?: string;
  searchPlaceholder?: string;
  isDisabled?: boolean;
  searchable?: boolean;
  onSelect?: (value: string) => void;
};

const LazySelect = forwardRef<HTMLDivElement, LazySelectProps<any>>(
  (
    {
      name,
      shouldShowError = true,
      params = [],
      useInfiniteQuery,
      getOptionLabel,
      getOptionValue,
      placeholder = "Select an option",
      searchPlaceholder = "Search...",
      isDisabled = false,
      searchable = true,
      onSelect,
      className,
    },
    ref,
  ) => {
    const { control } = useFormContext();
    const [search, setSearch] = useState("");
    const debouncedSearch = useDebounce(search);
    const [isOpen, setIsOpen] = useState(false);
    const [selectedOptionData, setSelectedOptionData] = useState<any | null>(
      null,
    );
    const [fieldValue, setFieldValue] = useState<string | undefined>();
    const observerTarget = useRef<HTMLDivElement>(null);
    const scrollContainerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      setSearch("");
      setSelectedOptionData(null);
      setFieldValue(undefined);
    }, [params]);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: (open) => {
        setIsOpen(open);
        if (!open) {
          // Clear search when dropdown is closed
          setSearch("");
        }
      },
      placement: "bottom-start",
      middleware: [
        offset(8),
        flip({
          fallbackPlacements: ["top-start"],
          fallbackStrategy: "bestFit",
          padding: 1,
          crossAxis: false,
        }),
        shift({
          padding: 1,
        }),
        size({
          apply({ rects, elements }) {
            Object.assign(elements.floating.style, {
              width: `${rects.reference.width}px`,
            });
          },
          padding: 1,
        }),
      ],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      useClick(context),
      useDismiss(context),
    ]);

    const { data, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
      useInfiniteQuery(debouncedSearch, ...params);

    const baseOptions = useMemo(() => {
      if (!data) return [];
      return data.pages.flatMap((page) => page.results);
    }, [data]);

    useEffect(() => {
      if (fieldValue) {
        if (
          selectedOptionData &&
          getOptionValue(selectedOptionData) === fieldValue
        ) {
          return;
        }

        const option = baseOptions.find(
          (opt) => getOptionValue(opt) === fieldValue,
        );

        if (option) {
          setSelectedOptionData(option);
        }
      } else {
        setSelectedOptionData(null);
      }
    }, [fieldValue, baseOptions, selectedOptionData, getOptionValue]);

    const filteredOptions = useMemo(() => {
      // When searchable is true and there's a search term, filter options client-side
      // Otherwise, return all options (server-side filtering is handled by the API)
      return searchable && debouncedSearch
        ? baseOptions.filter((opt) =>
            getOptionLabel(opt)
              .toLowerCase()
              .includes(debouncedSearch.toLowerCase()),
          )
        : baseOptions;
    }, [baseOptions, debouncedSearch, getOptionLabel, searchable]);

    useEffect(() => {
      if (!isOpen) return;

      const timeoutId = setTimeout(() => {
        const scrollContainer = scrollContainerRef.current;
        const target = observerTarget.current;

        if (!scrollContainer || !target) return;

        const observer = new IntersectionObserver(
          (entries) => {
            const [entry] = entries;
            if (entry.isIntersecting && !isFetchingNextPage && hasNextPage) {
              fetchNextPage();
            }
          },
          {
            root: scrollContainer,
            rootMargin: "0px",
            threshold: 0.1,
          },
        );

        observer.observe(target);

        return () => {
          observer.disconnect();
        };
      }, 300);

      return () => {
        clearTimeout(timeoutId);
      };
    }, [
      fetchNextPage,
      hasNextPage,
      isFetchingNextPage,
      debouncedSearch,
      isOpen,
      searchable,
    ]);

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          // Use useEffect to sync field value to avoid render-time state updates
          useEffect(() => {
            if (field.value !== fieldValue) {
              setFieldValue(field.value);
            }
          }, [field.value, fieldValue]);

          return (
            <div
              className={cn(
                "relative",
                hasError && shouldShowError && "mb-5",
                className,
              )}
              ref={ref}
            >
              {/* Custom Select Button */}
              <div
                ref={refs.setReference}
                {...getReferenceProps()}
                className="h-full w-full"
              >
                <button
                  type="button"
                  className={cn(
                    "group flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-1",
                    isDisabled
                      ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500"
                      : "border-gray-300 hover:bg-gray-50",
                    hasError
                      ? "border-red-500 bg-red-50 hover:!bg-red-50 focus:ring-red-500"
                      : "focus:border-blue-500 focus:bg-blue-50 focus:ring-blue-500",
                    "h-11 transition-all duration-200 ease-in-out",
                  )}
                  disabled={isDisabled}
                >
                  <span
                    className={cn(
                      "truncate",
                      !selectedOptionData && "text-gray-500",
                    )}
                  >
                    {selectedOptionData
                      ? getOptionLabel(selectedOptionData)
                      : placeholder}
                  </span>
                  {selectedOptionData ? (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedOptionData(null);
                        setFieldValue(undefined);
                        field.onChange(undefined);
                        onSelect?.(undefined as any);
                      }}
                    >
                      <MdOutlineClear className="size-4" />
                    </button>
                  ) : (
                    <ChevronDown
                      className={cn(
                        "size-4 shrink-0 transition-transform duration-200",
                        "text-gray-400",
                        "group-focus:text-blue-500",
                        hasError && "!text-red-500",
                        isOpen ? "-rotate-180 transform" : "",
                      )}
                    />
                  )}
                </button>

                {hasError && shouldShowError && (
                  <span className="absolute left-0 top-full mt-1 text-sm text-red-500">
                    {errorMessage}
                  </span>
                )}
              </div>

              {/* Dropdown Panel */}
              {isOpen && (
                <FloatingPortal>
                  <FloatingFocusManager context={context} modal={false}>
                    <div
                      ref={refs.setFloating}
                      style={floatingStyles}
                      {...getFloatingProps()}
                      className="z-50 rounded-lg border border-gray-200 bg-white py-1 pt-2.5 shadow-lg"
                    >
                      {/* Search Input */}
                      {searchable && (
                        <div className="px-3 pb-2">
                          <input
                            type="text"
                            className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border border-gray-200 px-3 py-2 text-sm focus:outline-none focus:ring-1"
                            placeholder={searchPlaceholder}
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                          />
                        </div>
                      )}

                      {/* Options List */}
                      <div
                        ref={scrollContainerRef}
                        className="select-options max-h-60 overflow-y-auto"
                      >
                        {isLoading ? (
                          <div className="flex items-center justify-center py-4">
                            <div className="border-t-primary-600 h-5 w-5 animate-spin rounded-full border-2 border-gray-300" />
                          </div>
                        ) : filteredOptions.length > 0 ? (
                          <>
                            {filteredOptions.map((option) => {
                              const isSelected =
                                fieldValue === getOptionValue(option);
                              return (
                                <button
                                  key={getOptionValue(option)}
                                  type="button"
                                  className={cn(
                                    "w-full px-3 py-2 text-left text-sm",
                                    "hover:bg-gray-100",
                                    isSelected
                                      ? "bg-primary-50 text-primary-600"
                                      : "text-gray-900",
                                  )}
                                  onClick={() => {
                                    const value = getOptionValue(option);
                                    setSelectedOptionData(option);
                                    setFieldValue(value);
                                    field.onChange(value);
                                    onSelect?.(value);
                                    setIsOpen(false);
                                    setSearch(""); // Clear search when selecting an option
                                  }}
                                >
                                  <span className="block truncate">
                                    {getOptionLabel(option)}
                                  </span>
                                </button>
                              );
                            })}
                            {/* Always show loading indicator when fetching next page */}
                            {isFetchingNextPage && (
                              <div className="py-2">
                                <div className="flex items-center justify-center">
                                  <div className="border-t-primary-600 h-5 w-5 animate-spin rounded-full border-2 border-gray-300" />
                                </div>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="flex items-center justify-center px-3 py-4 text-sm text-gray-500">
                            No options found
                          </div>
                        )}
                        {/* Always render the sentinel element */}
                        <div
                          ref={observerTarget}
                          className="h-px"
                          aria-hidden="true"
                        />
                      </div>
                    </div>
                  </FloatingFocusManager>
                </FloatingPortal>
              )}
            </div>
          );
        }}
      />
    );
  },
);

LazySelect.displayName = "LazySelect";

export { LazySelect };
