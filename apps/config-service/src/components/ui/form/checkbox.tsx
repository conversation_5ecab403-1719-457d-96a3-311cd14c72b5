"use client";

import { Checkbox as FlowbiteCheckbox, Label } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

export interface CheckboxProps {
  name: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  shouldShowError?: boolean;
}

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    { name, label, disabled = false, className, shouldShowError = true },
    ref,
  ) => {
    const { control } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div className={cn("space-y-1", className)}>
              <div className="flex items-center gap-2">
                <FlowbiteCheckbox
                  {...field}
                  ref={ref}
                  id={name}
                  checked={field.value || false}
                  onChange={(e) => field.onChange(e.target.checked)}
                  disabled={disabled}
                  color={hasError ? "failure" : undefined}
                />
                {label && (
                  <Label
                    htmlFor={name}
                    className="cursor-pointer text-sm font-medium text-gray-900"
                  >
                    {label}
                  </Label>
                )}
              </div>
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500">{errorMessage}</span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

Checkbox.displayName = "Checkbox";

export { Checkbox };
