"use client";

import { Label, Textarea as FlowbiteTextarea } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

export interface TextareaProps {
  name: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  shouldShowError?: boolean;
  rows?: number;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      name,
      label,
      placeholder,
      disabled = false,
      required = false,
      className,
      shouldShowError = true,
      rows = 3,
    },
    ref,
  ) => {
    const { control } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div className={cn("space-y-1", className)}>
              {label && (
                <Label
                  htmlFor={name}
                  className="text-sm font-medium text-gray-900"
                >
                  {label}
                  {required && <span className="text-red-500">*</span>}
                </Label>
              )}
              <FlowbiteTextarea
                {...field}
                ref={ref}
                id={name}
                placeholder={placeholder}
                disabled={disabled}
                rows={rows}
                color={hasError ? "failure" : undefined}
                helperText={hasError && shouldShowError ? errorMessage : undefined}
              />
            </div>
          );
        }}
      />
    );
  },
);

Textarea.displayName = "Textarea";

export { Textarea };
