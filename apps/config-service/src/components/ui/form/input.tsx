"use client";

import { Label, TextInput } from "flowbite-react";
import { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

export interface InputProps {
  name: string;
  label?: string;
  placeholder?: string;
  type?: "text" | "email" | "password" | "number" | "date";
  disabled?: boolean;
  required?: boolean;
  className?: string;
  shouldShowError?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      name,
      label,
      placeholder,
      type = "text",
      disabled = false,
      required = false,
      className,
      shouldShowError = true,
    },
    ref,
  ) => {
    const { control } = useFormContext();

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div className={cn("space-y-1", className)}>
              {label && (
                <Label
                  htmlFor={name}
                  value={label}
                  className={cn(
                    "block text-sm font-medium text-gray-900",
                    required &&
                      "after:ml-0.5 after:text-red-500 after:content-['*']",
                  )}
                />
              )}
              <TextInput
                {...field}
                ref={ref}
                id={name}
                type={type}
                placeholder={placeholder}
                disabled={disabled}
                color={hasError ? "failure" : undefined}
                className={cn(hasError && shouldShowError && "mb-5")}
              />
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500">{errorMessage}</span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

Input.displayName = "Input";

export { Input };
