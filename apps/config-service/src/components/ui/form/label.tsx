"use client";

import { Label as FlowbiteLabel } from "flowbite-react";
import { forwardRef } from "react";

import { cn } from "@/lib/utils";

export interface LabelProps extends React.ComponentProps<typeof FlowbiteLabel> {
  required?: boolean;
}

const Label = forwardRef<HTMLLabelElement, LabelProps>(
  ({ children, className, required, ...props }, ref) => {
    return (
      <FlowbiteLabel
        {...props}
        ref={ref}
        className={cn("text-sm font-medium text-gray-900", className)}
      >
        {children}
        {required && <span className="text-red-500 ml-1">*</span>}
      </FlowbiteLabel>
    );
  },
);

Label.displayName = "Label";

export { Label };
