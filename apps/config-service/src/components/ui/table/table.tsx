"use client";
import type {
  ColumnDef,
  ColumnFiltersState,
  RowSelectionState,
  SortingState,
  Table as TableType,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Table } from "flowbite-react";
import { ArrowUp, ArrowUpDown } from "lucide-react";
import React, { useImperativeHandle, useState } from "react";

import { cn } from "@/lib/utils";

export type TableInstance<TData> = {
  table: TableType<TData>;
};

type TableDataProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  tableRef?: React.ForwardedRef<TableInstance<TData>>;
  headCellStyle?: string;
  headStyle?: string;
  enableSorting?: boolean;
  isLoading?: boolean;
  className?: string;
  draggedRowId?: string | null;
  customEmptyCard?: React.ReactNode;
};

const TableData = <TData, TValue>({
  data,
  columns,
  tableRef,
  headCellStyle = "text-gray-500 p-4 !rounded-none",
  headStyle,
  enableSorting = false,
  isLoading = false,
  className,
  draggedRowId = null,
  customEmptyCard,
}: TableDataProps<TData, TValue>) => {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true,
    manualSorting: enableSorting,
    autoResetPageIndex: false,
    getRowId: (row) => (row as unknown as { id: string }).id,
    onRowSelectionChange: setRowSelection,
    state: {
      columnFilters,
      columnVisibility,
      rowSelection,
      sorting,
    },
  });

  useImperativeHandle(tableRef, () => ({ table }), [table]);

  return (
    <div className={cn("flex flex-1 flex-col overflow-x-auto", className)}>
      <Table>
        <Table.Head className={headStyle}>
          {table.getHeaderGroups().map((headerGroup) => (
            <React.Fragment key={headerGroup.id}>
              {headerGroup.headers.map((header, index) => {
                return (
                  <Table.HeadCell
                    key={`${header.id}-${index}`}
                    className={cn(
                      headCellStyle,
                      enableSorting &&
                        header.column.getCanSort() &&
                        "cursor-pointer",
                      "whitespace-nowrap border-y px-6 py-3 font-medium capitalize leading-[18px]",
                    )}
                    style={{
                      width: (header.column.columnDef.meta as any)?.width,
                      minWidth: (header.column.columnDef.meta as any)?.width,
                    }}
                    onClick={
                      enableSorting
                        ? () => {
                            if (!header.column.getCanSort()) return;
                            header.column.toggleSorting();
                          }
                        : undefined
                    }
                  >
                    <div className="flex gap-2">
                      <span>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}{" "}
                      </span>
                      {enableSorting && header.column.getCanSort() && (
                        <span>
                          {header.column.getCanSort() &&
                            !header.column.getIsSorted() && (
                              <ArrowUpDown className="size-4" />
                            )}
                          {header.column.getIsSorted() === "asc" && (
                            <ArrowUp className="size-4" />
                          )}
                          {header.column.getIsSorted() === "desc" && (
                            <ArrowUp className="size-4 rotate-180" />
                          )}
                        </span>
                      )}
                    </div>
                  </Table.HeadCell>
                );
              })}
            </React.Fragment>
          ))}
        </Table.Head>

        <Table.Body className="divide-y dark:divide-gray-700">
          {table.getRowModel().rows?.length
            ? table.getRowModel().rows.map((row, index) => {
                return (
                  <Table.Row
                    key={`${row.id}-${index}`}
                    data-state={row.getIsSelected() && "selected"}
                    className={cn(
                      "even:bg-gray-50 dark:even:bg-gray-700/20",
                      draggedRowId === row.id && "opacity-10",
                    )}
                  >
                    {row.getVisibleCells().map((cell, idx) => (
                      <Table.Cell
                        key={`${cell.id}-${idx}`}
                        style={{
                          width: (cell.column.columnDef.meta as any)?.width,
                          minWidth: (cell.column.columnDef.meta as any)?.width,
                        }}
                        className="text-primary-500 py-3 font-medium"
                      >
                        {isLoading ? (
                          <div className="h-4 animate-pulse rounded bg-gray-200" />
                        ) : (
                          flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )
                        )}
                      </Table.Cell>
                    ))}
                  </Table.Row>
                );
              })
            : null}
        </Table.Body>
      </Table>
      {table.getRowModel().rows?.length === 0 &&
        (customEmptyCard || (
          <div className="flex items-center justify-center py-8">
            <p className="text-gray-500">No data available</p>
          </div>
        ))}
    </div>
  );
};

TableData.displayName = "TableData";

export { TableData };
