"use client";

import { cn } from "@/lib/utils";

interface ActiveStatusBadgeProps {
  isActive: boolean;
  className?: string;
}

export const ActiveStatusBadge = ({
  isActive,
  className,
}: ActiveStatusBadgeProps) => {
  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
        isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800",
        className,
      )}
    >
      {isActive ? "Active" : "Inactive"}
    </span>
  );
};
