import { Modal as FlowbiteModal } from "flowbite-react";

import { cn } from "@/lib/utils";

const ModalHeader = (
  props: React.ComponentProps<typeof FlowbiteModal.Header>,
) => (
  <FlowbiteModal.Header
    {...props}
    className={cn("border-none p-5 pb-0", props.className)}
    theme={{
      ...props.theme,
      title: "font-plus-jakarta text-[32px] font-bold leading-[48px]",
    }}
  />
);
const ModalBody = (props: React.ComponentProps<typeof FlowbiteModal.Body>) => (
  <FlowbiteModal.Body {...props} className={cn("my-6", props.className)} />
);
const ModalFooter = (
  props: React.ComponentProps<typeof FlowbiteModal.Footer>,
) => (
  <FlowbiteModal.Footer
    {...props}
    className={cn("border-none p-5 pt-0", props.className)}
  />
);
const Modal = FlowbiteModal;

export { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ooter, ModalHeader };
