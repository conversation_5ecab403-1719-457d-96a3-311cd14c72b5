"use client";

import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import type { PropsWithChildren, ReactNode } from "react";
import {
  createContext,
  useCallback,
  useContext,
  useId,
  useMemo,
  useState,
} from "react";

import { cn } from "@/lib/utils";

// ======================= Types =======================
type AccordionContextType = {
  openItems: string[];
  toggleItem: (value: string) => void;
  isItemOpen: (value: string) => boolean;
  type: "single" | "multiple";
  defaultValue?: string | string[];
  collapsible?: boolean;
};

type AccordionProps = PropsWithChildren<{
  type?: "single" | "multiple";
  defaultValue?: string | string[];
  value?: string | string[]; // Add controlled value support
  collapsible?: boolean;
  className?: string;
  onChange?: (value: string | string[]) => void;
  onValueChange?: (value: string | string[]) => void; // Add onValueChange alias
}>;

type AccordionItemContextType = {
  value: string;
  isOpen: boolean;
  toggle: () => void;
};

type AccordionItemProps = PropsWithChildren<{
  value: string;
  className?: string;
  disabled?: boolean;
}>;

type AccordionTriggerProps = PropsWithChildren<{
  className?: string;
  icon?: ReactNode;
}>;

type AccordionContentProps = PropsWithChildren<{
  className?: string;
  forceMount?: boolean;
}>;

// ======================= Context =======================
const AccordionContext = createContext<AccordionContextType | null>(null);

const AccordionItemContext = createContext<AccordionItemContextType | null>(
  null,
);

// ======================= Hooks =======================
const useAccordionContext = () => {
  const context = useContext(AccordionContext);
  if (!context) {
    throw new Error(
      "useAccordionContext must be used within an AccordionProvider",
    );
  }
  return context;
};

const useAccordionItemContext = () => {
  const context = useContext(AccordionItemContext);
  if (!context) {
    throw new Error(
      "useAccordionItemContext must be used within an AccordionItemProvider",
    );
  }
  return context;
};

// ======================= Components =======================
export const Accordion = ({
  children,
  type = "single",
  defaultValue,
  value,
  collapsible = true,
  className,
  onChange,
  onValueChange,
}: AccordionProps) => {
  // Initialize open items based on value (controlled) or defaultValue (uncontrolled)
  const [internalOpenItems, setInternalOpenItems] = useState<string[]>(() => {
    if (value !== undefined) {
      return type === "single" ? [value as string] : (value as string[]);
    }
    if (type === "single") {
      return defaultValue ? [defaultValue as string] : [];
    }
    return (defaultValue as string[]) || [];
  });

  // Use controlled value if provided, otherwise use internal state
  const openItems = useMemo(() => {
    return value !== undefined
      ? type === "single"
        ? [value as string]
        : (value as string[])
      : internalOpenItems;
  }, [value, type, internalOpenItems]);

  // Toggle item open/closed
  const toggleItem = useCallback(
    (itemValue: string) => {
      const updateState = (prev: string[]) => {
        // For single type, either set to the new value or empty if collapsible
        if (type === "single") {
          const isOpen = prev.includes(itemValue);
          if (isOpen && collapsible) {
            const newValue: string[] = [];
            onChange?.(newValue[0] || "");
            onValueChange?.(newValue[0] || "");
            return newValue;
          }
          const newValue = [itemValue];
          onChange?.(itemValue);
          onValueChange?.(itemValue);
          return newValue;
        }

        // For multiple type, toggle the value
        const newValue = prev.includes(itemValue)
          ? prev.filter((item) => item !== itemValue)
          : [...prev, itemValue];

        onChange?.(newValue);
        onValueChange?.(newValue);
        return newValue;
      };

      // If controlled (value is provided), only call callbacks
      if (value !== undefined) {
        const currentItems =
          type === "single" ? [value as string] : (value as string[]);
        updateState(currentItems);
      } else {
        // If uncontrolled, update internal state
        setInternalOpenItems(updateState);
      }
    },
    [type, collapsible, onChange, onValueChange, value],
  );

  // Check if an item is open
  const isItemOpen = useCallback(
    (value: string) => {
      return openItems.includes(value);
    },
    [openItems],
  );

  return (
    <AccordionContext.Provider
      value={{
        openItems,
        toggleItem,
        isItemOpen,
        type,
        defaultValue,
        collapsible,
      }}
    >
      <div className={cn("w-full", className)}>{children}</div>
    </AccordionContext.Provider>
  );
};

export const AccordionItem = ({
  children,
  value,
  className,
  disabled = false,
}: AccordionItemProps) => {
  const { isItemOpen, toggleItem } = useAccordionContext();
  const isOpen = isItemOpen(value);

  const toggle = useCallback(() => {
    if (!disabled) {
      toggleItem(value);
    }
  }, [disabled, toggleItem, value]);

  return (
    <AccordionItemContext.Provider value={{ value, isOpen, toggle }}>
      <div
        className={cn(
          "border-b border-gray-200",
          disabled && "cursor-not-allowed opacity-50",
          className,
        )}
      >
        {children}
      </div>
    </AccordionItemContext.Provider>
  );
};

export const AccordionTrigger = ({
  children,
  className,
  icon,
}: AccordionTriggerProps) => {
  const { isOpen, toggle } = useAccordionItemContext();

  return (
    <button
      type="button"
      onClick={toggle}
      className={cn(
        "flex w-full select-none items-center justify-between px-5 py-4 text-left font-medium",
        "focus-visible:ring-primary-500 hover:bg-gray-50 focus:outline-none focus-visible:ring-2",
        className,
      )}
      aria-expanded={isOpen}
    >
      {children}
      {icon || (
        <ChevronDown
          className={cn(
            "h-5 w-5 shrink-0 transition-transform duration-200",
            isOpen && "rotate-180",
          )}
        />
      )}
    </button>
  );
};

export const AccordionContent = ({
  children,
  className,
  forceMount,
}: AccordionContentProps) => {
  const { isOpen } = useAccordionItemContext();
  const id = useId();

  return (
    <AnimatePresence initial={false}>
      {(forceMount || isOpen) && (
        <motion.div
          key={id}
          initial={{ height: 0, opacity: 0 }}
          animate={{
            height: "auto",
            opacity: 1,
            transition: {
              height: { duration: 0.2 },
              opacity: { duration: 0.2, delay: 0.05 },
            },
          }}
          exit={{
            height: 0,
            opacity: 0,
            transition: {
              height: { duration: 0.2 },
              opacity: { duration: 0.1 },
            },
          }}
          className="overflow-hidden"
        >
          <div className={cn("px-5 py-4", className)}>{children}</div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
