"use client";
import {
  Database,
  FileSliders,
  FolderClock,
  MessageSquare,
  Package,
  RefreshCcw,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { LogoTM } from "@/components/icons/logo-tm";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useSidebarContext } from "@/contexts/sidebar-context";

function cn(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

const navigationItems = [
  {
    label: "Recent Changes",
    href: "/recent-changes",
    icon: (
      <FolderClock
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
  },
  {
    label: "Sync Status",
    href: "/sync-status",
    icon: (
      <RefreshCcw
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
  },
];

const configurationItems = [
  {
    label: "AI Prompts",
    icon: (
      <MessageSquare
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/configuration/ai-prompts",
  },
  {
    label: "Prompt Templates",
    icon: (
      <FileSliders
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/configuration/prompt-templates",
  },
  {
    label: "Artifact Categories",
    icon: (
      <Package
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/configuration/artifact-categories",
  },
];

const referenceModelItems = [
  {
    label: "ISF Reference Models",
    icon: (
      <Database
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/reference-models/isf",
  },
  {
    label: "TMF Reference Models",
    icon: (
      <Database
        className="text-gray-700 group-[.active]:text-blue-700"
        size={16}
      />
    ),
    href: "/reference-models/tmf",
  },
];

export const ConfigSidebar = () => {
  const pathname = usePathname();
  const { accordionValue, onAccordionValueChange } = useSidebarContext();

  return (
    <aside className="fixed inset-y-0 left-0 z-20 flex h-full w-80 flex-col border-r border-gray-200 bg-white shadow-lg transition-[width] duration-200 ease-in-out dark:border-gray-700">
      {/* Logo */}
      <div className="flex items-center justify-center border-b border-gray-200 p-5">
        <Link href="/" className="flex items-center">
          <LogoTM
            className="mr-3 h-8 fill-gray-800 dark:fill-white"
            width={152}
            height={27}
          />
        </Link>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <nav className="p-4">
          {/* Regular navigation items */}
          <div className="mb-4 space-y-1">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "active group bg-blue-100 text-blue-700"
                      : "text-gray-700 hover:bg-gray-50",
                  )}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.label}
                </Link>
              );
            })}
          </div>

          {/* Accordion sections */}
          <Accordion
            type="multiple"
            value={accordionValue}
            onValueChange={onAccordionValueChange}
            className="space-y-2"
          >
            {/* Configuration Section */}
            <AccordionItem value="configuration" className="border-none">
              <AccordionTrigger className="rounded-md !px-3 !py-2 text-sm font-medium text-slate-500 hover:bg-gray-50">
                <div className="flex items-center uppercase">Configuration</div>
              </AccordionTrigger>
              <AccordionContent className="px-0 py-0">
                <div className="space-y-1">
                  {configurationItems.map((item) => {
                    const isActive = pathname === item.href;
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                          "flex items-center rounded-md px-3 py-2 text-sm transition-colors",
                          isActive
                            ? "active group bg-blue-100 font-medium text-blue-700"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                        )}
                      >
                        <span className="mr-3">{item.icon}</span>
                        {item.label}
                      </Link>
                    );
                  })}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Reference Models Section */}
            <AccordionItem value="reference-models" className="border-none">
              <AccordionTrigger className="rounded-md px-3 py-2 text-sm font-medium text-slate-500 hover:bg-gray-50">
                <div className="flex items-center uppercase">
                  Reference Models
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-0 py-0">
                <div className="space-y-1">
                  {referenceModelItems.map((item) => {
                    const isActive = pathname === item.href;
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                          "flex items-center rounded-md px-3 py-2 text-sm transition-colors",
                          isActive
                            ? "active group bg-blue-100 font-medium text-blue-700"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                        )}
                      >
                        <span className="mr-3">{item.icon}</span>
                        {item.label}
                      </Link>
                    );
                  })}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </nav>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4">
        <div className="text-center text-xs text-gray-700">
          Clincove Config Service
        </div>
      </div>
    </aside>
  );
};
