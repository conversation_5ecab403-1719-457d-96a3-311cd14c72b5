/* eslint-disable @next/next/no-img-element */
"use client";
import { SignIn } from "@clerk/nextjs";
import { AiOutlineLoading } from "react-icons/ai";

import { useAuthentication } from "@/contexts/authentication";

export const SignInPageContent = () => {
  const { invalidRole } = useAuthentication();

  if (invalidRole) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <AiOutlineLoading className="h-10 w-10 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex h-full min-h-screen w-full">
      <div className="flex h-screen flex-1 flex-col items-center justify-center bg-white dark:bg-gray-800">
        <div className="lg:px-18 relative flex h-full w-full flex-col justify-center space-y-6 px-8 sm:px-12">
          <div className="flex items-center justify-center [&_input]:rounded-md">
            <SignIn />
          </div>
        </div>
      </div>
      <div className="relative hidden h-screen flex-1 md:block">
        <img
          src="/images/authentication/right-content.png"
          alt="right-content"
          className="h-full w-full object-cover"
        />
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <img src="/images/authentication/large-logo.svg" alt="logo" />
        </div>
      </div>
    </div>
  );
};
