"use client";

import { useMutation } from "@tanstack/react-query";

import { useAuthentication } from "@/contexts/authentication";
import { AuthApi } from "@/lib/apis/auth/auth";

const authApi = new AuthApi();

export const useAuthenticated = () => {
  return useMutation({
    mutationFn: () => authApi.authenticated(),
    onSuccess: (data) => {
      useAuthentication.setState({
        authenticated: true,
        user: data,
        error: undefined,
        invalidRole: false,
      });
    },
    onError: (error) => {
      useAuthentication.setState({
        authenticated: false,
        user: undefined,
        error: error as Error,
        invalidRole: false,
      });
    },
  });
};
