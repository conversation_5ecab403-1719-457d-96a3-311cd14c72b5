"use client";

import { useAuth } from "@clerk/nextjs";
import { useEffect } from "react";
import { clearTimeout } from "timers";

import { clearAllStores } from "@/utils/clear-stores";

const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes

export const useInactivity = () => {
  const { signOut } = useAuth();

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const resetTimer = () => {
      if (!timeoutId) return;

      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleInactivity, INACTIVITY_TIMEOUT);
    };

    const handleInactivity = async () => {
      try {
        await signOut();
        clearAllStores();
        window.location.href = "/authentication/sign-in";
      } catch (error) {
        console.error("Error signing out due to inactivity:", error);
      }
    };

    const handleActivity = () => {
      resetTimer();
    };

    // Add event listeners for user activity
    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "scroll",
      "touchstart",
    ];
    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initialize timer
    resetTimer();

    return () => {
      clearTimeout(timeoutId);
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [signOut]);
};
