# Build and dependency directories
node_modules
dist
.next
out
.nx/cache
.nx/workspace-data
tmp
package-lock.json
bun.lockb
yarn.lock

# Environment and local files
.env
.env*.local
.vercel
.clinerules/

# System files
.DS_Store
.idea/

# Debug logs
npm-debug.log*

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Debug logs
npm-debug.log*

# Other
certificates

# augment rules
.augment-guidelines
.augmentignore
.augment

# Claude code
CLAUDE.md
.claude

# Windsurf
.windsurf

# Playwright
test-results/
playwright-report/
blob-report/
playwright/.cache/

# Forge AI
forge.yaml
